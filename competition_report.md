# 第六届人脸反欺骗研讨会：统一物理-数字攻击检测竞赛@ICCV2025

## 竞赛概述

### 竞赛背景
第六届人脸反欺骗研讨会是ICCV2025的重要组成部分，专注于统一物理-数字攻击检测技术的发展。随着人脸识别技术的广泛应用，各种欺骗攻击手段不断涌现，从传统的物理攻击（如打印照片、面具）到新兴的数字攻击（如深度伪造、对抗样本），对人脸识别系统的安全性提出了严峻挑战。

### 竞赛目标
本竞赛旨在推动人脸反欺骗技术的发展，特别是在统一检测物理攻击和数字攻击方面的能力。参赛者需要开发能够准确区分真实人脸和各种类型欺骗攻击的算法模型。

## 数据集详细分析

### 数据集规模
- **训练集**：22,367张图像（Protocol-train.txt）
- **验证集**：5,396张图像（Protocol-val.txt）
- **总计**：27,763张图像

### 标签体系结构

#### 主要类别分布
1. **真实人脸 (Live Face)**
   - 标签：0_0_0
   - 数量：839张 (3.75%)
   - 描述：正常的真实人脸图像

2. **物理攻击 (Physical Attack)**
   - 总数：231张 (1.03%)
   - 子类别：
     - **2D攻击**：
       - 1_0_0：Print (打印攻击) - 43张 (0.19%)
       - 1_0_1：Replay (重放攻击) - 109张 (0.49%)
       - 1_0_2：Cutouts (剪切攻击) - 79张 (0.35%)
     - **3D攻击**：
       - 1_1_0：Transparent (透明面具攻击)
       - 1_1_1：Plaster (石膏面具攻击)
       - 1_1_2：Resin (树脂面具攻击)

3. **数字攻击 (Digital Attack)**
   - 总数：21,297张 (95.22%)
   - 子类别：
     - **数字操作 (Digital Manipulation)**：9,176张 (41.02%)
       - 2_0_0：Attribute-Edit (属性编辑攻击) - 1,476张 (6.60%)
       - 2_0_1：Face-Swap (人脸替换攻击) - 6,160张 (27.54%)
       - 2_0_2：Video-Driven (视频驱动攻击) - 1,540张 (6.89%)
     - **数字对抗 (Digital Adversarial)**：12,121张 (54.19%)
       - 2_1_0：Pixel-Level (像素级对抗攻击) - 8,364张 (37.39%)
       - 2_1_1：Semantic-Level (语义级对抗攻击) - 3,757张 (16.80%)
     - **数字生成 (Digital Generation)**：
       - 2_2_0：ID_Consistent (身份一致性生成攻击)
       - 2_2_1：Style (风格化生成攻击)
       - 2_2_2：Prompt (提示词生成攻击)

### 数据集特点分析
1. **数字攻击占主导**：数字攻击样本占训练集的95.22%，反映了当前深度学习时代数字欺骗技术的普遍性
2. **对抗攻击突出**：像素级和语义级对抗攻击占比最高，体现了对抗样本在人脸欺骗中的重要地位
3. **物理攻击样本稀少**：传统物理攻击仅占1.03%，但仍是重要的安全威胁
4. **类别不平衡**：真实人脸样本仅占3.75%，存在显著的类别不平衡问题

## 评估标准

### 评估指标
竞赛采用ISO/IEC 30107-3标准化指标：

1. **APCER (Attack Presentation Classification Error Rate)**
   - 攻击样本分类错误率
   - 衡量将欺骗攻击误判为真实人脸的错误率

2. **NPCER/BPCER (Normal/Bona Fide Presentation Classification Error Rate)**
   - 正常样本分类错误率
   - 衡量将真实人脸误判为攻击的错误率

3. **ACER (Average Classification Error Rate)**
   - 平均分类错误率
   - ACER = (APCER + BPCER) / 2
   - **主要排名指标**

4. **AUC (Area Under Curve)**
   - ROC曲线下面积
   - 取值范围：0.5-1.0
   - 辅助评估指标

### 排名规则
- **主要排名依据**：ACER值（越小越好）
- **辅助参考**：AUC值（越大越好）

## 竞赛阶段与提交要求

### 第一阶段 (Phase 1)
**时间**：2025年5月20日 - 2025年6月13日

**任务**：
- 使用Protocol-train.txt训练模型
- 对Protocol-val.txt中的5,396张图像进行预测
- 提交预测分数文件

**提交格式**：
```
Data-val/00000.png 0.15361
Data-val/00001.png 0.23456
...
Data-val/05395.png 0.23394
```
- 每行包含图像路径和预测分数（真实人脸概率）
- 总计5,396行
- 压缩为ZIP文件提交

### 第二阶段 (Phase 2)
**时间**：2025年6月13日 - 2025年6月22日

**任务**：
- 发布测试数据Protocol-test.txt
- 发布验证集标签Protocol-val.txt
- 提交验证集和测试集的合并预测结果

**提交格式**：
```
Data-val/00000.png 0.15361
...
Data-val/05395.png 0.23394
Data-test/00000.png 0.15361
...
Data-test/09082.png 0.23394
```
- 总计14,479行（验证集5,396行 + 测试集9,083行）

## 竞赛规则与限制

### 技术限制
1. **禁止使用外部数据**：不允许使用任何额外的训练数据或预训练模型
2. **单模型限制**：只能使用一个深度学习模型，不允许模型融合
3. **计算复杂度限制**：单个模型的计算成本不得超过100G FLOPs

### 参赛要求
1. **团队注册**：使用正式邮箱（公司/大学邮箱）注册
2. **机构限制**：同一机构最多3个团队
3. **成员限制**：每个参赛者只能加入一个团队
4. **审批制**：需要组织者审批后才能参赛

## 技术挑战与研究意义

### 主要挑战
1. **统一检测框架**：需要设计能够同时检测物理攻击和数字攻击的统一框架
2. **类别不平衡**：真实人脸样本稀少，需要有效的不平衡学习策略
3. **攻击多样性**：从简单的打印攻击到复杂的对抗样本，攻击手段多样化
4. **泛化能力**：模型需要在未见过的攻击类型上保持良好性能

### 研究价值
1. **推动技术发展**：促进人脸反欺骗技术向统一检测方向发展
2. **标准化评估**：建立统一的评估标准和基准数据集
3. **实际应用**：为实际部署的人脸识别系统提供安全保障
4. **学术贡献**：为相关研究提供高质量的数据集和评估框架

## 总结

第六届人脸反欺骗研讨会竞赛代表了当前人脸安全领域的最新发展趋势，通过统一物理-数字攻击检测的挑战，推动了相关技术的进步。数据集的设计充分反映了现实世界中人脸欺骗攻击的复杂性和多样性，为研究者提供了宝贵的研究平台。竞赛的严格规则和标准化评估体系确保了结果的公平性和可比性，对推动人脸反欺骗技术的发展具有重要意义。
