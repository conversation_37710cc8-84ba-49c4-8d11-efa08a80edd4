# 第六届人脸反欺骗研讨会：统一物理-数字攻击检测竞赛@ICCV2025

## 竞赛概述

### 竞赛背景
第六届人脸反欺骗研讨会是ICCV2025的重要组成部分，专注于统一物理-数字攻击检测技术的发展。随着人脸识别技术的广泛应用，各种欺骗攻击手段不断涌现，从传统的物理攻击（如打印照片、面具）到新兴的数字攻击（如深度伪造、对抗样本），对人脸识别系统的安全性提出了严峻挑战。

### 竞赛目标
本竞赛旨在推动人脸反欺骗技术的发展，特别是在统一检测物理攻击和数字攻击方面的能力。参赛者需要开发能够准确区分真实人脸和各种类型欺骗攻击的算法模型。

## 数据集详细统计分析

### 数据集规模概览
| 数据集类型 | 文件名 | 样本数量 | 标签状态 |
|-----------|--------|----------|----------|
| 训练集 | Protocol-train.txt | 22,367 | 已标注 |
| 验证集 | Protocol-val.txt | 5,396 | 未标注 |
| **总计** | - | **27,763** | - |

### 训练集详细标签分布

#### 完整标签统计表
| 标签代码 | 攻击类型 | 中文描述 | 样本数量 | 占训练集比例 | 占总数据比例 |
|----------|----------|----------|----------|-------------|-------------|
| 0_0_0 | Live Face | 真实人脸 | 839 | 3.75% | 3.02% |
| 1_0_0 | Print | 打印攻击 | 43 | 0.19% | 0.15% |
| 1_0_1 | Replay | 重放攻击 | 109 | 0.49% | 0.39% |
| 1_0_2 | Cutouts | 剪切攻击 | 79 | 0.35% | 0.28% |
| 2_0_0 | Attribute-Edit | 属性编辑攻击 | 1,476 | 6.60% | 5.32% |
| 2_0_1 | Face-Swap | 人脸替换攻击 | 6,160 | 27.54% | 22.19% |
| 2_0_2 | Video-Driven | 视频驱动攻击 | 1,540 | 6.89% | 5.55% |
| 2_1_0 | Pixel-Level | 像素级对抗攻击 | 8,364 | 37.39% | 30.13% |
| 2_1_1 | Semantic-Level | 语义级对抗攻击 | 3,757 | 16.80% | 13.53% |

#### 层次分类统计表
| 主类别 | 子类别 | 样本数量 | 占训练集比例 | 包含的具体攻击类型 |
|--------|--------|----------|-------------|-------------------|
| **Live** | - | 839 | 3.75% | 真实人脸 |
| **Physical Attack** | 2D Attack | 231 | 1.03% | Print, Replay, Cutouts |
| | 3D Attack | 0 | 0.00% | Transparent, Plaster, Resin |
| **Digital Attack** | Digital Manipulation | 9,176 | 41.02% | Attribute-Edit, Face-Swap, Video-Driven |
| | Digital Adversarial | 12,121 | 54.19% | Pixel-Level, Semantic-Level |
| | Digital Generation | 0 | 0.00% | ID_Consistent, Style, Prompt |

### 攻击类型详细技术解释

#### 1. 真实人脸 (Live Face)
- **标签**: 0_0_0
- **定义**: 正常的真实人脸图像，无任何欺骗攻击
- **特征**: 自然光照、真实纹理、正常生理特征

#### 2. 物理攻击 (Physical Attack)

##### 2D物理攻击
- **1_0_0 打印攻击 (Print Attack)**
  - **原理**: 使用打印的人脸照片进行欺骗
  - **特征**: 平面纹理、反光特性、边缘效应
  - **检测难点**: 高质量打印材料的逼真度

- **1_0_1 重放攻击 (Replay Attack)**
  - **原理**: 使用屏幕播放人脸视频进行欺骗
  - **特征**: 屏幕摩尔纹、像素化、刷新频率
  - **检测难点**: 高分辨率屏幕的视觉效果

- **1_0_2 剪切攻击 (Cutouts Attack)**
  - **原理**: 在照片上剪切眼部等关键区域制造"活体"效果
  - **特征**: 不自然的边缘、深度不连续
  - **检测难点**: 精细的剪切工艺

##### 3D物理攻击
- **1_1_0 透明面具攻击 (Transparent Mask)**
  - **原理**: 使用透明材料制作的3D面具
  - **特征**: 材质反射、厚度效应

- **1_1_1 石膏面具攻击 (Plaster Mask)**
  - **原理**: 使用石膏等材料制作的刚性面具
  - **特征**: 表面粗糙度、重量分布

- **1_1_2 树脂面具攻击 (Resin Mask)**
  - **原理**: 使用树脂材料制作的柔性面具
  - **特征**: 材质光泽、弹性变形

#### 3. 数字攻击 (Digital Attack)

##### 数字操作攻击 (Digital Manipulation)
- **2_0_0 属性编辑攻击 (Attribute-Edit)**
  - **原理**: 通过编辑软件修改人脸属性（年龄、性别、表情等）
  - **技术**: Photoshop、FaceApp等工具
  - **特征**: 局部不一致性、编辑痕迹

- **2_0_1 人脸替换攻击 (Face-Swap)**
  - **原理**: 将一个人的脸替换到另一个人的头部
  - **技术**: DeepFakes、FaceSwapper等
  - **特征**: 边界融合痕迹、光照不一致

- **2_0_2 视频驱动攻击 (Video-Driven)**
  - **原理**: 使用驱动视频控制目标人脸的表情和动作
  - **技术**: First Order Motion Model、Face2Face等
  - **特征**: 运动不自然、时序不连贯

##### 数字对抗攻击 (Digital Adversarial)
- **2_1_0 像素级对抗攻击 (Pixel-Level Adversarial)**
  - **原理**: 在像素级别添加精心设计的扰动
  - **技术**: FGSM、PGD、C&W等算法
  - **特征**: 微小但精确的像素修改

- **2_1_1 语义级对抗攻击 (Semantic-Level Adversarial)**
  - **原理**: 在语义层面进行对抗性修改
  - **技术**: 语义感知的对抗样本生成
  - **特征**: 保持视觉自然性的同时欺骗模型

##### 数字生成攻击 (Digital Generation)
- **2_2_0 身份一致性生成攻击 (ID_Consistent Generation)**
  - **原理**: 生成与特定身份一致的虚假人脸
  - **技术**: StyleGAN、身份保持生成模型

- **2_2_1 风格化生成攻击 (Style Generation)**
  - **原理**: 生成特定风格的虚假人脸
  - **技术**: 风格迁移、艺术化生成

- **2_2_2 提示词生成攻击 (Prompt Generation)**
  - **原理**: 基于文本提示生成虚假人脸
  - **技术**: CLIP、DALL-E等多模态生成模型

### 数据集特点深度分析

#### 类别不平衡问题
| 类别类型 | 样本数量 | 占比 | 不平衡程度 |
|----------|----------|------|------------|
| 真实人脸 | 839 | 3.75% | 严重不足 |
| 物理攻击 | 231 | 1.03% | 极度稀少 |
| 数字攻击 | 21,297 | 95.22% | 严重过多 |

#### 攻击复杂度分析
- **低复杂度攻击**: 物理2D攻击（231样本，1.03%）
- **中等复杂度攻击**: 数字操作攻击（9,176样本，41.02%）
- **高复杂度攻击**: 数字对抗攻击（12,121样本，54.19%）

## 评估标准与指标体系

### ISO/IEC 30107-3标准化评估指标

#### 1. APCER (Attack Presentation Classification Error Rate)
**定义**: 攻击样本分类错误率

**数学公式**:
```
APCER = (攻击样本被误判为真实的数量) / (总攻击样本数量)
APCER = FN_attack / N_attack
```

**详细说明**:
- **FN_attack**: 被误判为真实人脸的攻击样本数量（假阴性）
- **N_attack**: 总攻击样本数量
- **意义**: 衡量系统对攻击的漏检率，值越小表示系统越安全
- **安全影响**: 高APCER意味着攻击者更容易绕过系统

#### 2. BPCER/NPCER (Bona Fide/Normal Presentation Classification Error Rate)
**定义**: 正常样本分类错误率

**数学公式**:
```
BPCER = (真实样本被误判为攻击的数量) / (总真实样本数量)
BPCER = FP_live / N_live
```

**详细说明**:
- **FP_live**: 被误判为攻击的真实人脸样本数量（假阳性）
- **N_live**: 总真实人脸样本数量
- **意义**: 衡量系统对正常用户的误拒率，值越小表示用户体验越好
- **用户体验影响**: 高BPCER导致正常用户频繁被拒绝

#### 3. ACER (Average Classification Error Rate)
**定义**: 平均分类错误率

**数学公式**:
```
ACER = (APCER + BPCER) / 2
```

**详细说明**:
- **平衡指标**: 综合考虑安全性和可用性
- **主要排名依据**: 竞赛以ACER作为主要评估标准
- **理想值**: 0（完美分类）
- **实际意义**: 在安全性和用户体验之间寻求平衡

#### 4. AUC (Area Under Curve)
**定义**: ROC曲线下面积

**数学公式**:
```
AUC = ∫[0,1] TPR(FPR) d(FPR)
其中：
TPR = TP / (TP + FN)  # 真阳性率
FPR = FP / (FP + TN)  # 假阳性率
```

**详细说明**:
- **取值范围**: [0.5, 1.0]
- **0.5**: 随机分类器性能
- **1.0**: 完美分类器性能
- **实际意义**: 衡量分类器在所有阈值下的综合性能
- **优势**: 不受类别不平衡影响，适合本数据集

### 评估指标对比表
| 指标 | 公式 | 取值范围 | 最优值 | 主要用途 | 权重 |
|------|------|----------|--------|----------|------|
| APCER | FN_attack/N_attack | [0,1] | 0 | 安全性评估 | 50% |
| BPCER | FP_live/N_live | [0,1] | 0 | 可用性评估 | 50% |
| ACER | (APCER+BPCER)/2 | [0,1] | 0 | **主排名指标** | 100% |
| AUC | ROC曲线积分 | [0.5,1] | 1 | 辅助参考 | - |

### 排名规则详解
1. **主要排名**: 按ACER升序排列（越小越好）
2. **平局处理**: ACER相同时，按AUC降序排列（越大越好）
3. **最终目标**: 在保证安全性的前提下，最大化用户体验

## 竞赛阶段与提交要求详解

### 竞赛时间线
| 阶段 | 开始时间 | 结束时间 | 持续时间 | 主要任务 |
|------|----------|----------|----------|----------|
| Phase 1 | 2025年5月20日 00:00 UTC | 2025年6月13日 00:00 UTC | 24天 | 模型开发与验证 |
| Phase 2 | 2025年6月13日 00:00 UTC | 2025年6月22日 00:00 UTC | 9天 | 最终测试与提交 |

### 第一阶段 (Phase 1) - 开发阶段

#### 阶段目标
- **模型训练**: 使用Protocol-train.txt（22,367样本）训练反欺骗模型
- **性能验证**: 在Protocol-val.txt（5,396样本）上验证模型性能
- **结果提交**: 提交验证集预测结果，获得在线反馈

#### 数据使用规范
| 数据文件 | 用途 | 样本数量 | 标签状态 | 使用限制 |
|----------|------|----------|----------|----------|
| Protocol-train.txt | 模型训练 | 22,367 | 已提供 | 仅用于训练 |
| Protocol-val.txt | 模型验证 | 5,396 | 未提供 | 仅用于预测 |

#### 提交文件格式详解

**文件命名**: 任意名称（建议使用团队名称）
**文件格式**: ZIP压缩包
**压缩要求**: 直接压缩预测文件，不要添加文件夹

**预测文件内容格式**:
```
Data-val/00000.png 0.15361
Data-val/00001.png 0.23456
Data-val/00002.png 0.87234
...
Data-val/05395.png 0.23394
```

**格式规范说明**:
- **总行数**: 必须为5,396行（与Protocol-val.txt一致）
- **每行格式**: `<图像路径> <预测分数>`
- **分隔符**: 单个空格
- **图像路径**: 必须与Protocol-val.txt中的路径完全一致
- **预测分数**: 浮点数，表示样本为真实人脸的概率
- **分数范围**: 建议[0,1]，但不强制要求
- **精度**: 建议保留5位小数

**示例对照表**:
| 行号 | Protocol-val.txt | 预测文件格式 | 说明 |
|------|------------------|--------------|------|
| 1 | Data-val/00000.png | Data-val/00000.png 0.15361 | 第一行 |
| 2 | Data-val/00001.png | Data-val/00001.png 0.23456 | 第二行 |
| ... | ... | ... | ... |
| 5396 | Data-val/05395.png | Data-val/05395.png 0.23394 | 最后一行 |

### 第二阶段 (Phase 2) - 测试阶段

#### 阶段目标
- **数据发布**: 组织者发布Protocol-test.txt和Protocol-val.txt标签
- **模型选择**: 参赛者根据验证集标签选择最佳模型
- **最终提交**: 提交验证集和测试集的合并预测结果

#### 新增数据
| 数据文件 | 发布时间 | 样本数量 | 用途 |
|----------|----------|----------|------|
| Protocol-test.txt | Phase 2开始 | 9,083 | 最终测试 |
| Protocol-val.txt标签 | Phase 2开始 | 5,396 | 模型选择 |

#### 合并提交格式

**文件结构**:
```
Data-val/00000.png 0.15361    # 验证集开始（第1行）
Data-val/00001.png 0.23456
...
Data-val/05395.png 0.23394    # 验证集结束（第5,396行）
Data-test/00000.png 0.15361   # 测试集开始（第5,397行）
Data-test/00001.png 0.67890
...
Data-test/09082.png 0.23394   # 测试集结束（第14,479行）
```

**合并规范**:
- **总行数**: 14,479行（5,396 + 9,083）
- **顺序要求**: 先验证集，后测试集
- **连续性**: 按列直接拼接，无分隔行
- **路径前缀**: 验证集用"Data-val/"，测试集用"Data-test/"

**行号对应表**:
| 数据集 | 起始行号 | 结束行号 | 样本数量 | 路径前缀 |
|--------|----------|----------|----------|----------|
| 验证集 | 1 | 5,396 | 5,396 | Data-val/ |
| 测试集 | 5,397 | 14,479 | 9,083 | Data-test/ |

### 提交系统与反馈

#### CodaLab提交流程
1. **文件准备**: 按格式要求准备预测文件
2. **压缩打包**: 创建ZIP文件（不含文件夹）
3. **在线提交**: 通过CodaLab系统上传
4. **结果反馈**: 系统自动计算并显示评估结果

#### 反馈信息内容
- **APCER**: 攻击样本分类错误率
- **BPCER**: 真实样本分类错误率
- **ACER**: 平均分类错误率（排名依据）
- **AUC**: ROC曲线下面积
- **排名**: 当前排行榜位置

## 竞赛规则与限制详解

### 严格技术限制

#### 1. 数据使用限制
| 限制类型 | 具体要求 | 违规后果 |
|----------|----------|----------|
| **外部数据** | 禁止使用任何额外训练数据 | 取消参赛资格 |
| **预训练模型** | 禁止使用在外部数据上预训练的模型 | 取消参赛资格 |
| **数据增强** | 仅允许基本几何变换和颜色变换 | 需要详细说明 |

#### 2. 模型架构限制
| 限制项目 | 要求 | 检查方式 |
|----------|------|----------|
| **模型数量** | 仅允许使用单个深度学习模型 | 代码审查 |
| **模型融合** | 禁止任何形式的模型集成 | 代码审查 |
| **计算复杂度** | 不超过100G FLOPs | 自动计算验证 |

#### 3. FLOPs计算说明
**定义**: 浮点运算次数（Floating Point Operations）
**计算范围**: 包括前向传播的所有运算
**测试条件**: 输入图像尺寸224×224×3
**验证工具**: 使用torchprofile或类似工具计算

### 参赛资格与团队管理

#### 参赛申请流程
1. **邮箱验证**: 必须使用正式机构邮箱（.edu, .org, .com等）
2. **信息提交**: 提供团队名称、机构名称、成员信息
3. **审批等待**: 组织者审核（通常1-3个工作日）
4. **数据获取**: 审批通过后获得下载链接

#### 团队组建规则
| 规则类型 | 具体限制 | 说明 |
|----------|----------|------|
| **机构限制** | 每个机构最多3个团队 | 包括公司、大学、研究所 |
| **成员互斥** | 每人只能加入一个团队 | 严格执行，违者取消资格 |
| **团队规模** | 建议3-5人 | 无强制限制 |

#### 禁用邮箱类型
- Gmail (@gmail.com)
- 新浪邮箱 (@sina.com)
- QQ邮箱 (@qq.com)
- 其他个人邮箱服务

### 学术诚信与公平竞争

#### 代码提交要求
- **Phase 2结束后**: 提交完整训练和推理代码
- **代码审查**: 组织者将进行详细审查
- **可复现性**: 必须能够复现提交的结果
- **文档要求**: 提供详细的README和环境配置

#### 排名公布机制
- **实时排名**: Phase 1期间显示实时排行榜
- **最终排名**: Phase 2结束后公布最终结果
- **零分处理**: 未提交代码的团队记为零分
- **公开透明**: 所有有效团队的成绩都将公布

## 技术挑战深度分析

### 核心技术挑战

#### 1. 统一检测框架设计
**挑战描述**: 物理攻击和数字攻击的特征差异巨大
- **物理攻击特征**: 材质纹理、光照反射、几何形变
- **数字攻击特征**: 像素级伪影、频域异常、时序不一致
- **解决思路**: 多尺度特征融合、注意力机制、域适应技术

#### 2. 极端类别不平衡处理
**数据分布问题**:
- 真实人脸: 3.75% (严重不足)
- 数字攻击: 95.22% (严重过多)
- 物理攻击: 1.03% (极度稀少)

**技术解决方案**:
- **重采样策略**: SMOTE、ADASYN等
- **损失函数**: Focal Loss、Class-Balanced Loss
- **数据增强**: 针对性的少数类增强

#### 3. 对抗攻击鲁棒性
**攻击类型占比**:
- 像素级对抗: 37.39% (最高比例)
- 语义级对抗: 16.80%

**防御策略**:
- **对抗训练**: 在训练中加入对抗样本
- **特征去噪**: 使用去噪自编码器
- **集成防御**: 多模型投票（受单模型限制）

#### 4. 跨攻击类型泛化
**泛化难点**:
- 训练集中某些攻击类型样本为0
- 新兴攻击手段不断出现
- 攻击技术快速演进

**提升策略**:
- **元学习**: Few-shot learning方法
- **域泛化**: 学习攻击无关的特征
- **自监督学习**: 利用无标签数据

### 研究价值与学术意义

#### 1. 技术创新推动
- **统一框架**: 首次大规模统一物理-数字攻击检测
- **评估标准**: 建立ISO标准化的评估体系
- **基准数据**: 提供高质量的多攻击类型数据集

#### 2. 实际应用价值
- **金融安全**: 银行人脸支付系统安全
- **门禁系统**: 企业和住宅安全控制
- **移动设备**: 手机人脸解锁安全
- **在线身份**: 远程身份验证系统

#### 3. 学术研究贡献
- **新方法验证**: 为新算法提供标准测试平台
- **性能基准**: 建立技术发展的参考标准
- **研究方向**: 指导未来研究重点和方向

## 竞赛总结与展望

### 竞赛特色与创新点

#### 数据集创新
- **规模最大**: 27,763张图像，涵盖9种攻击类型
- **类型最全**: 首次统一物理和数字攻击
- **标准最高**: 采用ISO/IEC 30107-3国际标准

#### 评估体系创新
- **指标科学**: ACER平衡安全性和可用性
- **流程规范**: 两阶段评估确保公平性
- **反馈及时**: 实时排行榜促进技术交流

#### 技术要求创新
- **限制严格**: 单模型、无外部数据限制
- **计算约束**: 100G FLOPs限制促进高效算法
- **代码开源**: 促进技术透明和可复现

### 对人脸安全领域的影响

#### 短期影响（1-2年）
- 推动统一检测算法的快速发展
- 建立新的技术评估标准
- 促进产业界技术升级

#### 长期影响（3-5年）
- 形成人脸反欺骗技术的新范式
- 推动相关国际标准的制定
- 为下一代安全系统奠定基础

### 未来发展方向

#### 技术发展趋势
1. **多模态融合**: 结合RGB、红外、深度等多种模态
2. **实时检测**: 面向移动设备的轻量化算法
3. **自适应防御**: 能够应对未知攻击的自学习系统
4. **隐私保护**: 在保护用户隐私的前提下进行检测

#### 应用拓展前景
1. **边缘计算**: 在IoT设备上部署反欺骗算法
2. **云端服务**: 提供API形式的反欺骗服务
3. **标准制定**: 推动行业标准和法规的完善
4. **教育培训**: 为安全从业者提供专业培训

---

**第六届人脸反欺骗研讨会竞赛**不仅是一次技术竞赛，更是人脸安全领域的重要里程碑。通过严格的技术要求、科学的评估体系和丰富的数据集，本竞赛将推动人脸反欺骗技术向更加统一、高效、实用的方向发展，为构建更加安全的数字世界贡献重要力量。
