# 第六届人脸反欺骗研讨会：统一物理-数字攻击检测竞赛@ICCV2025

## 竞赛概述

**目标**: 开发统一检测物理攻击和数字攻击的人脸反欺骗算法
**时间**: 2025年5月20日 - 2025年6月22日
**平台**: CodaLab

## 数据集统计分析

### 数据集规模
| 数据集 | 文件名 | 样本数量 | 标签状态 |
|--------|--------|----------|----------|
| 训练集 | Protocol-train.txt | 22,367 | 已标注 |
| 验证集 | Protocol-val.txt | 5,396 | 未标注 |
| **总计** | - | **27,763** | - |

### 训练集标签分布
| 标签代码 | 攻击类型 | 中文描述 | 样本数量 | 占训练集比例 |
|----------|----------|----------|----------|-------------|
| 0_0_0 | Live Face | 真实人脸 | 839 | 3.75% |
| 1_0_0 | Print | 打印攻击 | 43 | 0.19% |
| 1_0_1 | Replay | 重放攻击 | 109 | 0.49% |
| 1_0_2 | Cutouts | 剪切攻击 | 79 | 0.35% |
| 2_0_0 | Attribute-Edit | 属性编辑攻击 | 1,476 | 6.60% |
| 2_0_1 | Face-Swap | 人脸替换攻击 | 6,160 | 27.54% |
| 2_0_2 | Video-Driven | 视频驱动攻击 | 1,540 | 6.89% |
| 2_1_0 | Pixel-Level | 像素级对抗攻击 | 8,364 | 37.39% |
| 2_1_1 | Semantic-Level | 语义级对抗攻击 | 3,757 | 16.80% |

### 层次分类统计
| 主类别 | 子类别 | 样本数量 | 占比 |
|--------|--------|----------|------|
| **Live** | - | 839 | 3.75% |
| **Physical Attack** | 2D Attack | 231 | 1.03% |
| **Digital Attack** | Digital Manipulation | 9,176 | 41.02% |
| | Digital Adversarial | 12,121 | 54.19% |

### 攻击类型技术解释

#### 物理攻击 (Physical Attack)
- **1_0_0 打印攻击**: 使用打印照片进行欺骗，特征：平面纹理、反光特性
- **1_0_1 重放攻击**: 使用屏幕播放视频进行欺骗，特征：摩尔纹、像素化
- **1_0_2 剪切攻击**: 在照片上剪切关键区域制造活体效果，特征：不自然边缘

#### 数字攻击 (Digital Attack)
- **2_0_0 属性编辑攻击**: 修改人脸属性（年龄、性别等），技术：Photoshop、FaceApp
- **2_0_1 人脸替换攻击**: 将一个人的脸替换到另一个人头部，技术：DeepFakes
- **2_0_2 视频驱动攻击**: 使用驱动视频控制目标人脸表情，技术：Face2Face
- **2_1_0 像素级对抗攻击**: 在像素级添加精心设计的扰动，技术：FGSM、PGD
- **2_1_1 语义级对抗攻击**: 在语义层面进行对抗性修改，保持视觉自然性

## 评估标准

### ISO/IEC 30107-3标准化指标

#### 核心指标公式
```
APCER = FN_attack / N_attack    # 攻击样本分类错误率
BPCER = FP_live / N_live        # 真实样本分类错误率  
ACER = (APCER + BPCER) / 2      # 平均分类错误率（主排名指标）
AUC = ∫[0,1] TPR(FPR) d(FPR)    # ROC曲线下面积
```

#### 指标说明
| 指标 | 取值范围 | 最优值 | 用途 | 权重 |
|------|----------|--------|------|------|
| APCER | [0,1] | 0 | 安全性评估 | 50% |
| BPCER | [0,1] | 0 | 可用性评估 | 50% |
| **ACER** | [0,1] | 0 | **主排名指标** | 100% |
| AUC | [0.5,1] | 1 | 辅助参考 | - |

### 排名规则
1. **主要排名**: 按ACER升序排列（越小越好）
2. **平局处理**: ACER相同时，按AUC降序排列（越大越好）

## 竞赛阶段与提交格式

### 时间安排
| 阶段 | 时间 | 持续 | 任务 |
|------|------|------|------|
| Phase 1 | 5月20日-6月13日 | 24天 | 模型开发与验证 |
| Phase 2 | 6月13日-6月22日 | 9天 | 最终测试与提交 |

### Phase 1 提交格式
**文件要求**: ZIP压缩包，包含5,396行预测结果
```
Data-val/00000.png 0.15361
Data-val/00001.png 0.23456
...
Data-val/05395.png 0.23394
```

**格式规范**:
- 总行数: 5,396行
- 每行格式: `<图像路径> <预测分数>`
- 分隔符: 单个空格
- 预测分数: 真实人脸概率，建议[0,1]范围

### Phase 2 提交格式
**文件要求**: ZIP压缩包，包含14,479行合并结果
```
Data-val/00000.png 0.15361    # 验证集（第1-5,396行）
...
Data-val/05395.png 0.23394
Data-test/00000.png 0.15361   # 测试集（第5,397-14,479行）
...
Data-test/09082.png 0.23394
```

## 竞赛规则与限制

### 技术限制
| 限制类型 | 具体要求 | 违规后果 |
|----------|----------|----------|
| **外部数据** | 禁止使用任何额外训练数据 | 取消参赛资格 |
| **预训练模型** | 禁止使用外部数据预训练模型 | 取消参赛资格 |
| **模型数量** | 仅允许单个深度学习模型 | 取消参赛资格 |
| **计算复杂度** | 不超过100G FLOPs | 取消参赛资格 |

### 参赛要求
- **邮箱**: 必须使用正式机构邮箱（禁用Gmail、QQ邮箱等）
- **团队限制**: 每个机构最多3个团队，每人只能加入一个团队
- **代码提交**: Phase 2结束后提交完整训练和推理代码

## 核心技术挑战

### 1. 极端类别不平衡
- 真实人脸: 3.75%（严重不足）
- 数字攻击: 95.22%（严重过多）
- 物理攻击: 1.03%（极度稀少）

### 2. 统一检测框架
- 物理攻击特征: 材质纹理、光照反射、几何形变
- 数字攻击特征: 像素级伪影、频域异常、时序不一致

### 3. 对抗攻击鲁棒性
- 像素级对抗攻击占37.39%（最高比例）
- 语义级对抗攻击占16.80%

### 技术解决方案
- **重采样策略**: SMOTE、ADASYN
- **损失函数**: Focal Loss、Class-Balanced Loss
- **对抗训练**: 在训练中加入对抗样本
- **多尺度特征融合**: 注意力机制、域适应技术
