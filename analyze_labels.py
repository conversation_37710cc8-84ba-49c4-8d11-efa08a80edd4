#!/usr/bin/env python3
"""
Face Anti-Spoofing Dataset Label Analysis
分析第六届人脸反欺骗研讨会数据集的标签分布
"""

import os
from collections import Counter, defaultdict

# 标签映射字典
LABEL_MAPPING = {
    # Live
    '0_0_0': 'Live Face',

    # Physical Attack (1_)
    '1_0_0': 'Print',
    '1_0_1': 'Replay',
    '1_0_2': 'Cutouts',
    '1_1_0': 'Transparent',
    '1_1_1': 'Plaster',
    '1_1_2': 'Resin',

    # Digital Attack (2_)
    '2_0_0': 'Attribute-Edit',
    '2_0_1': 'Face-Swap',
    '2_0_2': 'Video-Driven',
    '2_1_0': 'Pixel-Level',
    '2_1_1': 'Semantic-Level',
    '2_2_0': 'ID_Consistent',
    '2_2_1': 'Style',
    '2_2_2': 'Prompt'
}

# 层次分类
HIERARCHICAL_MAPPING = {
    # 主类别
    'Live': ['0_0_0'],
    'Physical Attack': ['1_0_0', '1_0_1', '1_0_2', '1_1_0', '1_1_1', '1_1_2'],
    'Digital Attack': ['2_0_0', '2_0_1', '2_0_2', '2_1_0', '2_1_1', '2_2_0', '2_2_1', '2_2_2'],

    # 子类别
    '2D Attack': ['1_0_0', '1_0_1', '1_0_2'],
    '3D Attack': ['1_1_0', '1_1_1', '1_1_2'],
    'Digital Manipulation': ['2_0_0', '2_0_1', '2_0_2'],
    'Digital Adversarial': ['2_1_0', '2_1_1'],
    'Digital Generation': ['2_2_0', '2_2_1', '2_2_2']
}

def analyze_protocol_file(file_path):
    """分析协议文件中的标签分布"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None

    labels = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:  # 训练集有标签
                    labels.append(parts[1])
                else:  # 验证集只有文件名
                    labels.append("unlabeled")

    return labels

def count_labels(labels):
    """统计标签数量"""
    return Counter(labels)

def count_hierarchical(labels):
    """按层次结构统计标签"""
    hierarchical_counts = defaultdict(int)

    for label in labels:
        if label == "unlabeled":
            hierarchical_counts["Unlabeled"] += 1
            continue

        # 统计主类别
        for main_category, sub_labels in HIERARCHICAL_MAPPING.items():
            if label in sub_labels:
                hierarchical_counts[main_category] += 1

    return dict(hierarchical_counts)

def print_statistics(title, label_counts, hierarchical_counts):
    """打印统计信息"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")

    total = sum(label_counts.values())
    print(f"总样本数: {total}")

    print(f"\n详细标签分布:")
    print(f"{'标签':<10} {'描述':<20} {'数量':<8} {'百分比':<8}")
    print("-" * 50)

    for label, count in sorted(label_counts.items()):
        description = LABEL_MAPPING.get(label, label)
        percentage = (count / total) * 100
        print(f"{label:<10} {description:<20} {count:<8} {percentage:<8.2f}%")

    print(f"\n层次分类统计:")
    print(f"{'类别':<20} {'数量':<8} {'百分比':<8}")
    print("-" * 40)

    for category, count in sorted(hierarchical_counts.items()):
        percentage = (count / total) * 100
        print(f"{category:<20} {count:<8} {percentage:<8.2f}%")

def main():
    """主函数"""
    print("第六届人脸反欺骗研讨会数据集标签分析")
    print("=" * 60)

    # 分析训练集
    train_file = "The_6th_Face_Anti-Spoofing_Workshop/phase1/Protocol-train.txt"
    train_labels = analyze_protocol_file(train_file)

    if train_labels:
        train_counts = count_labels(train_labels)
        train_hierarchical = count_hierarchical(train_labels)
        print_statistics("训练集 (Protocol-train.txt)", train_counts, train_hierarchical)

    # 分析验证集
    val_file = "The_6th_Face_Anti-Spoofing_Workshop/phase1/Protocol-val.txt"
    val_labels = analyze_protocol_file(val_file)

    if val_labels:
        val_counts = count_labels(val_labels)
        val_hierarchical = count_hierarchical(val_labels)
        print_statistics("验证集 (Protocol-val.txt)", val_counts, val_hierarchical)

    # 总体统计
    if train_labels and val_labels:
        all_labels = train_labels + val_labels
        all_counts = count_labels(all_labels)
        all_hierarchical = count_hierarchical(all_labels)
        print_statistics("总体统计 (训练集 + 验证集)", all_counts, all_hierarchical)

    print(f"\n{'='*60}")
    print("分析完成!")

if __name__ == "__main__":
    main()
